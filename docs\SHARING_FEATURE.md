# Native Share Functionality for Invoice PDF

## Overview

The invoice PDF feature now includes native sharing functionality that provides platform-specific sharing options:

- **Android**: Shows the native Android share sheet with available apps like Gmail, WhatsApp, Google Drive, etc.
- **iOS**: Shows the native iOS share sheet with available apps and actions
- **Web**: Uses the Web Share API when available, with fallbacks to download/clipboard

## Implementation Details

### Files Modified/Created

1. **`src/utils/shareUtils.ts`** - Main sharing utility with platform detection
2. **`src/utils/shareUtils.web.ts`** - Web-specific implementation
3. **`src/utils/shareUtils.native.ts`** - React Native specific implementation
4. **`src/screens/InvoicePdfScreen.tsx`** - Updated to use native sharing

### Dependencies Added

- `react-native-share`: Provides native sharing capabilities for iOS and Android

### Features

#### PDF File Sharing
- Generates PDF using jsPDF
- Creates shareable blob URL for web
- Uses native file sharing on mobile platforms
- Supports sharing through any app that accepts PDF files

#### Text Fallback Sharing
- If PDF sharing fails or no PDF is generated, falls back to text sharing
- Includes product details, quantities, and total amount
- Uses native text sharing capabilities

#### Platform-Specific Behavior

**Web Platform:**
- Uses Web Share API when available
- Falls back to file download for PDF sharing
- Falls back to clipboard copy for text sharing
- Shows user-friendly alerts for feedback

**iOS Platform:**
- Shows native iOS share sheet
- Supports sharing to Mail, Messages, AirDrop, etc.
- Respects user's installed apps and sharing preferences

**Android Platform:**
- Shows native Android share sheet
- Supports sharing to Gmail, WhatsApp, Google Drive, etc.
- Integrates with Android's sharing intents system

### User Experience

1. **Generate PDF**: User clicks "Generate PDF" button
   - PDF is created and downloaded
   - Blob URL is stored for sharing
   - Button shows loading state during generation

2. **Share Invoice**: User clicks "Share PDF" or "Share Invoice" button
   - If PDF exists: Shows native share sheet with PDF file
   - If no PDF: Shares text summary of invoice
   - Button shows loading state during sharing
   - Graceful fallbacks if sharing fails

### Error Handling

- Graceful degradation if native sharing is not available
- Fallback to text sharing if file sharing fails
- User-friendly error messages
- Console logging for debugging

### Memory Management

- Blob URLs are properly cleaned up on component unmount
- No memory leaks from generated PDF files

## Usage Example

```typescript
import { shareFile, shareText, getSharingCapabilities } from '../utils/shareUtils';

// Share a PDF file
const success = await shareFile({
  url: pdfBlobUrl,
  filename: 'invoice.pdf',
  title: 'PlomDesign Invoice',
  message: 'Invoice generated by PlomDesign Mobile',
  type: 'application/pdf',
});

// Share text content
const success = await shareText({
  title: 'PlomDesign Invoice',
  message: 'Invoice details...',
});

// Check capabilities
const capabilities = getSharingCapabilities();
console.log('Platform:', capabilities.platform);
console.log('Native sharing available:', capabilities.nativeShareAvailable);
```

## Testing

The sharing functionality can be tested on:

1. **Web browsers** with Web Share API support (Chrome, Safari, Edge)
2. **iOS devices** with native share sheet
3. **Android devices** with native share sheet
4. **Web browsers** without Web Share API (fallback behavior)

## Future Enhancements

- Support for sharing multiple file formats
- Custom share sheet options
- Integration with cloud storage services
- Batch sharing of multiple invoices
