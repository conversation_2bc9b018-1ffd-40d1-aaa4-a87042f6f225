const https = require('https');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PORT = 3000;

// Create self-signed certificate if it doesn't exist
function createSelfSignedCert() {
  const certPath = path.join(__dirname, 'cert.pem');
  const keyPath = path.join(__dirname, 'key.pem');
  
  if (!fs.existsSync(certPath) || !fs.existsSync(keyPath)) {
    console.log('Creating self-signed certificate...');
    try {
      execSync(`openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"`, { stdio: 'inherit' });
      console.log('Certificate created successfully!');
    } catch (error) {
      console.error('Failed to create certificate. Please install OpenSSL or create certificates manually.');
      console.log('Alternative: Use a tool like mkcert or serve the files through a different HTTPS server.');
      process.exit(1);
    }
  }
  
  return {
    key: fs.readFileSync(keyPath),
    cert: fs.readFileSync(certPath)
  };
}

// Simple static file server
function serveStaticFile(req, res, filePath) {
  const fullPath = path.join(__dirname, 'dist', filePath);
  
  if (!fs.existsSync(fullPath)) {
    res.writeHead(404);
    res.end('File not found');
    return;
  }
  
  const ext = path.extname(fullPath);
  const contentType = {
    '.html': 'text/html',
    '.js': 'application/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
  }[ext] || 'application/octet-stream';
  
  res.writeHead(200, { 'Content-Type': contentType });
  fs.createReadStream(fullPath).pipe(res);
}

// Create HTTPS server
function startServer() {
  const options = createSelfSignedCert();
  
  const server = https.createServer(options, (req, res) => {
    let filePath = req.url === '/' ? '/index.html' : req.url;
    
    // Remove query parameters
    filePath = filePath.split('?')[0];
    
    serveStaticFile(req, res, filePath);
  });
  
  server.listen(PORT, () => {
    console.log(`
🔒 HTTPS Server Started!
📱 URL: https://localhost:${PORT}
🌐 Access from mobile: https://[your-ip]:${PORT}

⚠️  Certificate Warning:
Your browser will show a security warning because we're using a self-signed certificate.
Click "Advanced" → "Proceed to localhost (unsafe)" to continue.

📋 Testing Web Share API:
1. Open https://localhost:${PORT} in your browser
2. Navigate to Invoice PDF screen
3. Generate a PDF
4. Click the Share button
5. The native share dialog should appear

🔧 Troubleshooting:
- Make sure you're using HTTPS (required for Web Share API)
- Test on mobile devices for best results
- Chrome/Edge have the best Web Share API support
- Safari on iOS also supports Web Share API well

Press Ctrl+C to stop the server.
    `);
  });
  
  server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
      console.error(`Port ${PORT} is already in use. Please stop other servers or use a different port.`);
    } else {
      console.error('Server error:', err);
    }
  });
}

// Check if dist folder exists
if (!fs.existsSync(path.join(__dirname, 'dist'))) {
  console.error('dist folder not found. Please run "npm run build" first.');
  process.exit(1);
}

startServer();
