<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Native Sharing Test - PlomDesign Mobile</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 10px 10px 0;
            transition: background 0.2s;
        }
        button:hover {
            background: #5a67d8;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .debug-info {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Native Sharing Test</h1>
        <p>This page tests the Web Share API functionality that was implemented in PlomDesign Mobile.</p>
        
        <div class="test-section">
            <h3>📱 Web Share API Support</h3>
            <div id="api-support"></div>
            <div id="debug-info" class="debug-info"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Native Sharing</h3>
            <p>Click the button below to test the native sharing functionality:</p>
            <button id="test-native-share">Test System Share</button>
            <button id="test-text-share">Test Text Share</button>
            <button id="test-url-share">Test URL Share</button>
            <div id="share-status"></div>
        </div>

        <div class="test-section">
            <h3>📋 Test Results</h3>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        // Debug Web Share API capabilities
        function debugWebShareAPI() {
            const debug = {
                navigatorShareExists: !!(navigator.share),
                navigatorCanShareExists: !!(navigator.canShare),
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                isSecureContext: window.isSecureContext,
                protocol: window.location.protocol,
                hostname: window.location.hostname,
                timestamp: new Date().toISOString()
            };
            
            console.log('Web Share API Debug Info:', debug);
            return debug;
        }

        // Test native sharing with different data types
        async function testNativeShare(shareData, testName) {
            console.log(`Testing ${testName} with data:`, shareData);
            
            const statusDiv = document.getElementById('share-status');
            const resultsDiv = document.getElementById('test-results');
            
            statusDiv.innerHTML = `<div class="status info">Testing ${testName}...</div>`;
            
            try {
                if (!navigator.share) {
                    throw new Error('Web Share API not available');
                }

                // Check if data can be shared
                if (navigator.canShare) {
                    const canShare = navigator.canShare(shareData);
                    console.log(`Can share ${testName}:`, canShare);
                    if (!canShare) {
                        throw new Error('Data cannot be shared according to canShare()');
                    }
                }

                console.log(`Calling navigator.share() for ${testName}...`);
                await navigator.share(shareData);
                
                const successMsg = `✅ ${testName} completed successfully`;
                console.log(successMsg);
                statusDiv.innerHTML = `<div class="status success">${successMsg}</div>`;
                resultsDiv.innerHTML += `<div class="status success">${new Date().toLocaleTimeString()}: ${successMsg}</div>`;
                
                return true;
            } catch (error) {
                console.error(`${testName} failed:`, error);
                
                let errorMsg = `❌ ${testName} failed: ${error.message}`;
                let errorClass = 'error';
                
                if (error.name === 'AbortError') {
                    errorMsg = `⚠️ ${testName} cancelled by user`;
                    errorClass = 'info';
                } else if (error.name === 'NotAllowedError') {
                    errorMsg = `❌ ${testName} not allowed (user gesture required)`;
                }
                
                statusDiv.innerHTML = `<div class="status ${errorClass}">${errorMsg}</div>`;
                resultsDiv.innerHTML += `<div class="status ${errorClass}">${new Date().toLocaleTimeString()}: ${errorMsg}</div>`;
                
                return false;
            }
        }

        // Initialize the page
        function init() {
            const debugInfo = debugWebShareAPI();
            
            // Display API support info
            const supportDiv = document.getElementById('api-support');
            if (debugInfo.navigatorShareExists) {
                supportDiv.innerHTML = `
                    <div class="status success">
                        ✅ Web Share API is available!<br>
                        Can Share API: ${debugInfo.navigatorCanShareExists ? 'Available' : 'Not available'}<br>
                        Secure Context: ${debugInfo.isSecureContext ? 'Yes' : 'No'}<br>
                        Protocol: ${debugInfo.protocol}
                    </div>
                `;
            } else {
                supportDiv.innerHTML = `
                    <div class="status error">
                        ❌ Web Share API is not available in this browser.<br>
                        This may be due to browser compatibility or security context.
                    </div>
                `;
            }
            
            // Display debug info
            document.getElementById('debug-info').textContent = JSON.stringify(debugInfo, null, 2);
            
            // Set up event listeners
            document.getElementById('test-native-share').addEventListener('click', () => {
                testNativeShare({
                    title: 'PlomDesign Invoice Test',
                    text: 'This is a test of the native sharing functionality from PlomDesign Mobile app.'
                }, 'Basic Native Share');
            });
            
            document.getElementById('test-text-share').addEventListener('click', () => {
                testNativeShare({
                    title: 'PlomDesign Invoice',
                    text: `PlomDesign Invoice - Test Canvas

Products:
1. Test Product (Qty: 2)
Category: Test / Category
Price: 10.00
Material: Test Material

Total: 20.00

Generated by PlomDesign Mobile`
                }, 'Text Share');
            });
            
            document.getElementById('test-url-share').addEventListener('click', () => {
                testNativeShare({
                    title: 'PlomDesign Mobile',
                    text: 'Check out this awesome mobile design app!',
                    url: 'https://github.com/your-repo/plomdesign-mobile'
                }, 'URL Share');
            });
            
            // Disable buttons if Web Share API is not available
            if (!debugInfo.navigatorShareExists) {
                document.querySelectorAll('button').forEach(btn => {
                    if (btn.id.startsWith('test-')) {
                        btn.disabled = true;
                    }
                });
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
