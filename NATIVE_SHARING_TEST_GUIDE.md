# Native Sharing Test Guide

## Overview
This guide helps you test the fixed native sharing functionality in PlomDesign Mobile. The "System Share" option should now properly trigger the browser's Web Share API to display the native sharing interface.

## What Was Fixed

### 1. User Gesture Context Preservation
- **Problem**: The modal was closing before calling `navigator.share()`, breaking the user gesture context
- **Solution**: For native sharing, we now prepare the data and call `navigator.share()` immediately while preserving the user gesture context

### 2. Enhanced Error Handling
- Added comprehensive error handling for different Web Share API error types:
  - `AbortError`: User cancelled (not treated as failure)
  - `NotAllowedError`: User gesture required
  - `DataError`: Invalid share data
  - Generic errors with detailed logging

### 3. Improved Data Validation
- Better validation of share data before calling `navigator.share()`
- Uses `navigator.canShare()` when available to pre-validate data
- Handles URL validation (only HTTP/HTTPS URLs)
- Fallback behavior when data cannot be shared

### 4. Debug Information
- Added `debugWebShareAPI()` function to log browser capabilities
- Console logging throughout the sharing process
- Better error messages for troubleshooting

## Testing Instructions

### Step 1: Open Developer Console
1. Open the PlomDesign Mobile app in your browser
2. Press F12 or right-click → "Inspect" to open Developer Tools
3. Go to the "Console" tab to see debug information

### Step 2: Navigate to Invoice Screen
1. Add some products to a canvas
2. Navigate to the Invoice PDF screen
3. Optionally generate a PDF first (this will enable PDF sharing)

### Step 3: Test Native Sharing
1. Click the "Share" button
2. Look for debug information in the console showing Web Share API capabilities
3. Click on "System Share" option
4. **Expected behavior**: 
   - The modal should close immediately
   - The browser's native share dialog should appear
   - You should see sharing options specific to your platform/browser

### Step 4: Verify Different Scenarios

#### Scenario A: With PDF Generated
1. Generate a PDF first by clicking "Generate PDF"
2. Then click "Share" → "System Share"
3. The share dialog should include the PDF URL and invoice details

#### Scenario B: Without PDF
1. Without generating a PDF, click "Share" → "System Share"
2. The share dialog should include a text summary of the invoice

#### Scenario C: User Cancellation
1. Click "Share" → "System Share"
2. When the native share dialog appears, press "Cancel" or "Escape"
3. Check console - should show "User cancelled native share" (not an error)

## Browser Compatibility

### Supported Browsers
- **Chrome/Edge 89+**: Full support
- **Safari 14+**: Full support on macOS/iOS
- **Firefox**: Limited support (may not work on all platforms)

### Platform-Specific Behavior
- **Windows**: Shows Windows 10/11 share dialog with installed apps
- **macOS**: Shows macOS share sheet with available services
- **Android**: Shows Android share sheet with installed apps
- **iOS**: Shows iOS share sheet with available apps

## Troubleshooting

### If Native Sharing Doesn't Work

1. **Check Console Logs**: Look for error messages and debug information
2. **Verify HTTPS**: Web Share API requires HTTPS (except localhost)
3. **Check Browser Support**: Ensure your browser supports Web Share API
4. **User Gesture**: Make sure you're clicking directly (not programmatically triggered)

### Common Issues and Solutions

#### Issue: "Native sharing not allowed (user gesture required)"
- **Cause**: The user gesture context was lost
- **Solution**: This should be fixed with the new implementation

#### Issue: "Invalid data for native share"
- **Cause**: The share data format is not supported
- **Solution**: The code now validates and adjusts data format automatically

#### Issue: "Web Share API not available"
- **Cause**: Browser doesn't support Web Share API
- **Solution**: Use other sharing options in the modal

## Debug Information

When you open the share modal, check the console for output like:
```
Web Share API Debug Info: {
  navigatorShareExists: true,
  navigatorCanShareExists: true,
  userAgent: "...",
  platform: "...",
  isSecureContext: true,
  protocol: "https:",
  hostname: "..."
}
```

This information helps identify compatibility issues.

## Expected Console Output

For successful native sharing:
```
handleShareOption called with: native
Attempting native share immediately to preserve user gesture
Sharing via native: {title: "...", text: "...", url: "..."}
Attempting native share with Web Share API
Native share data prepared: {title: "...", text: "...", url: "..."}
Can share check result: true
Calling navigator.share...
Native share completed successfully
Native share successful
```

## Fallback Behavior

If native sharing fails, the app will:
1. Show an appropriate error message
2. Suggest trying other sharing options
3. Keep the other sharing methods available in the modal

## Testing on Different Devices

1. **Desktop**: Test on Chrome, Edge, Safari, Firefox
2. **Mobile**: Test on mobile browsers (Chrome Mobile, Safari Mobile)
3. **PWA**: If installed as PWA, native sharing should work better

The native sharing functionality should now work reliably across supported browsers and platforms!
