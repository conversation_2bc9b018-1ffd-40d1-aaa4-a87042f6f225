<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sharing Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
        }
        button:hover {
            background: #5a67d8;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .info {
            background: #f3f4f6;
            padding: 16px;
            border-radius: 8px;
            margin: 16px 0;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <h1>🔗 Native Sharing Test</h1>
    
    <div class="info">
        <h3>Platform Capabilities</h3>
        <div id="capabilities">Loading...</div>
    </div>
    
    <div>
        <h3>Test Sharing Functions</h3>
        <button onclick="testTextSharing()">Share Text</button>
        <button onclick="testFileSharing()">Share File (PDF)</button>
        <button onclick="generateAndSharePDF()">Generate & Share PDF</button>
    </div>
    
    <div id="results"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script>
        // Mock sharing utilities for web testing
        const shareUtils = {
            shareText: async (options) => {
                try {
                    if (navigator.share && navigator.canShare) {
                        const shareData = {
                            title: options.title,
                            text: options.message,
                            url: options.url,
                        };
                        
                        if (navigator.canShare(shareData)) {
                            await navigator.share(shareData);
                            return true;
                        }
                    }
                    
                    if (navigator.clipboard && options.message) {
                        await navigator.clipboard.writeText(options.message);
                        alert('Content copied to clipboard!');
                        return true;
                    }
                    
                    alert(options.message || 'Share content');
                    return false;
                } catch (error) {
                    console.error('Error sharing text:', error);
                    return false;
                }
            },

            shareFile: async (options) => {
                try {
                    if (navigator.share && navigator.canShare) {
                        try {
                            const response = await fetch(options.url);
                            const blob = await response.blob();
                            const file = new File([blob], options.filename, { type: options.type || blob.type });
                            
                            const shareData = {
                                title: options.title,
                                text: options.message,
                                files: [file],
                            };
                            
                            if (navigator.canShare(shareData)) {
                                await navigator.share(shareData);
                                return true;
                            }
                        } catch (error) {
                            console.warn('Web file sharing failed, falling back to download:', error);
                        }
                    }
                    
                    const link = document.createElement('a');
                    link.href = options.url;
                    link.download = options.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    return true;
                } catch (error) {
                    console.error('Error sharing file:', error);
                    return false;
                }
            },

            getSharingCapabilities: () => {
                return {
                    platform: 'web',
                    nativeShareAvailable: !!(navigator.share && navigator.canShare),
                    canShareFiles: !!(navigator.share && navigator.canShare),
                    canShareText: true,
                    fallbackMethod: 'download/clipboard',
                };
            }
        };

        // Display capabilities
        function displayCapabilities() {
            const capabilities = shareUtils.getSharingCapabilities();

            // Additional browser info
            const browserInfo = {
                userAgent: navigator.userAgent,
                hasNavigatorShare: !!navigator.share,
                hasNavigatorCanShare: !!navigator.canShare,
                hasClipboard: !!navigator.clipboard,
                isSecureContext: window.isSecureContext,
                protocol: window.location.protocol
            };

            document.getElementById('capabilities').innerHTML = `
                <strong>Platform:</strong> ${capabilities.platform}<br>
                <strong>Native Share Available:</strong> ${capabilities.nativeShareAvailable ? '✅ Yes' : '❌ No'}<br>
                <strong>Can Share Files:</strong> ${capabilities.canShareFiles ? '✅ Yes' : '❌ No'}<br>
                <strong>Can Share Text:</strong> ${capabilities.canShareText ? '✅ Yes' : '❌ No'}<br>
                <strong>Fallback Method:</strong> ${capabilities.fallbackMethod}<br><br>

                <strong>Browser Details:</strong><br>
                <strong>navigator.share:</strong> ${browserInfo.hasNavigatorShare ? '✅' : '❌'}<br>
                <strong>navigator.canShare:</strong> ${browserInfo.hasNavigatorCanShare ? '✅' : '❌'}<br>
                <strong>navigator.clipboard:</strong> ${browserInfo.hasClipboard ? '✅' : '❌'}<br>
                <strong>Secure Context:</strong> ${browserInfo.isSecureContext ? '✅' : '❌'}<br>
                <strong>Protocol:</strong> ${browserInfo.protocol}<br>
                <small><strong>User Agent:</strong> ${browserInfo.userAgent}</small>
            `;
        }

        // Test functions
        async function testTextSharing() {
            showResult('Testing text sharing...', 'info');
            console.log('Starting text share test');

            try {
                const success = await shareUtils.shareText({
                    title: 'PlomDesign Test',
                    message: 'This is a test message from PlomDesign Mobile sharing functionality!',
                });
                console.log('Text share test result:', success);
                showResult(`Text sharing ${success ? 'succeeded' : 'failed'}`, success ? 'success' : 'error');
            } catch (error) {
                console.error('Text share test error:', error);
                showResult(`Text sharing failed with error: ${error.message}`, 'error');
            }
        }

        async function testFileSharing() {
            showResult('Testing file sharing...', 'info');
            // Create a simple text file for testing
            const blob = new Blob(['This is a test file for sharing'], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const success = await shareUtils.shareFile({
                url: url,
                filename: 'test-file.txt',
                title: 'Test File Share',
                message: 'Sharing a test file from PlomDesign',
                type: 'text/plain',
            });
            
            URL.revokeObjectURL(url);
            showResult(`File sharing ${success ? 'succeeded' : 'failed'}`, success ? 'success' : 'error');
        }

        async function generateAndSharePDF() {
            showResult('Generating PDF...', 'info');
            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                doc.setFontSize(20);
                doc.text('PlomDesign Test Invoice', 20, 30);
                doc.setFontSize(12);
                doc.text('This is a test PDF generated for sharing functionality testing.', 20, 50);
                doc.text('Generated on: ' + new Date().toLocaleString(), 20, 70);
                
                const pdfBlob = doc.output('blob');
                const pdfUrl = URL.createObjectURL(pdfBlob);
                
                showResult('PDF generated, testing share...', 'info');
                
                const success = await shareUtils.shareFile({
                    url: pdfUrl,
                    filename: 'plomdesign-test-invoice.pdf',
                    title: 'PlomDesign Test Invoice',
                    message: 'Test invoice generated by PlomDesign Mobile',
                    type: 'application/pdf',
                });
                
                URL.revokeObjectURL(pdfUrl);
                showResult(`PDF sharing ${success ? 'succeeded' : 'failed'}`, success ? 'success' : 'error');
            } catch (error) {
                showResult(`PDF generation failed: ${error.message}`, 'error');
            }
        }

        function showResult(message, type) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `info ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        // Initialize
        displayCapabilities();
    </script>
</body>
</html>
