<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</head>
<body>
    <h1>PDF Generation Test</h1>
    <button onclick="generatePDF()">Generate Test PDF</button>

    <script>
        function generatePDF() {
            try {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                doc.text('Hello World!', 20, 20);
                doc.text('This is a test PDF generated with jsPDF', 20, 30);
                
                doc.save('test.pdf');
                alert('PDF generated successfully!');
            } catch (error) {
                console.error('Error generating PDF:', error);
                alert('Error generating PDF: ' + error.message);
            }
        }
    </script>
</body>
</html>
