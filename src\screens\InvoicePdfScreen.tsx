import React, { useEffect, useRef, useState } from 'react';
import { useData } from '../contexts/DataContext';
import { useTheme } from '../contexts/ThemeContext';
import { Product, CanvasProduct } from '../types';
import { shareFile, shareText, getSharingCapabilities, shareViaMethod } from '../utils/shareUtils';
import ShareModal from '../components/ShareModal';
// @ts-ignore
import jsPDF from 'jspdf';

const InvoicePdfScreen: React.FC = () => {
  const { state } = useData();

  // Use only products added to the current canvas
  const canvasProducts: CanvasProduct[] = state.currentCanvas?.products || [];
  const { isDark } = useTheme();
  const fadeAnim = useRef(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [lastGeneratedPdfUrl, setLastGeneratedPdfUrl] = useState<string | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);

  // Helper function to extract base product ID from canvas product ID
  const getBaseProductId = (canvasProductId: string): string => {
    // Canvas product IDs are in format: canvasId_productId_timestamp_randomSuffix
    // We need to extract the original productId
    const parts = canvasProductId.split('_');
    if (parts.length >= 2) {
      // For new format: canvasId_productId_timestamp_randomSuffix
      if (parts.length >= 4) {
        return parts[1]; // Return the original product ID
      }
      // For old format: canvasId_productId
      return parts[1];
    }
    return canvasProductId; // Fallback to the full ID
  };

  // Aggregate products by base product ID and sum quantities
  const aggregatedProducts = canvasProducts.reduce((acc, product) => {
    const baseProductId = getBaseProductId(product.id);

    if (acc[baseProductId]) {
      // Product already exists, increment quantity
      acc[baseProductId].totalQuantity += product.quantity;
      acc[baseProductId].instances += 1;
    } else {
      // First instance of this product
      acc[baseProductId] = {
        ...product,
        baseProductId,
        totalQuantity: product.quantity,
        instances: 1,
      };
    }

    return acc;
  }, {} as Record<string, CanvasProduct & { baseProductId: string; totalQuantity: number; instances: number }>);

  // Convert aggregated products back to array
  const invoiceProducts = Object.values(aggregatedProducts);

  useEffect(() => {
    // Animation placeholder
  }, []);

  // Cleanup blob URL on unmount
  useEffect(() => {
    return () => {
      if (lastGeneratedPdfUrl) {
        URL.revokeObjectURL(lastGeneratedPdfUrl);
      }
    };
  }, [lastGeneratedPdfUrl]);

  const handleGeneratePdf = async () => {
    if (invoiceProducts.length === 0) {
      alert('No products to export. Please add products to the canvas first.');
      return;
    }

    setIsGenerating(true);
    try {
      // Create new PDF document
      const doc = new jsPDF();

      // Set document properties
      doc.setProperties({
        title: 'PlomDesign Invoice',
        subject: 'Product Invoice',
        author: 'PlomDesign Mobile',
        creator: 'PlomDesign Mobile App'
      });

      // Header with logo
      let headerStartY = 20;

      doc.setFontSize(20);
      doc.setFont('helvetica', 'bold');
      doc.text('INVOICE', 105, headerStartY, { align: 'center' });

      // Horizontal layout: Logo on left, Company info on right
      let yPos = 35;
      let companyInfoStartX = 65; // X position for company info (adjusted for smaller 30px logo container)
      let logoHeight = 30; // Height of logo container area (reduced for better quality)

      // Add logo with proper aspect ratio and maximum quality
      let actualLogoHeight = logoHeight; // Track actual logo height for spacing

      if (state.companyInfo?.logo) {
        try {
          // Process logo with proper aspect ratio fitting and maximum quality
          const logoResult = await new Promise<{width: number, height: number}>((resolve) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';

            img.onload = () => {
              try {
                // Define smaller container dimensions for better quality
                const maxWidth = 30;
                const maxHeight = 30;

                // Calculate aspect ratio preserving dimensions
                const aspectRatio = img.width / img.height;
                let displayWidth = maxWidth;
                let displayHeight = maxWidth / aspectRatio;

                // If height exceeds container, scale by height instead
                if (displayHeight > maxHeight) {
                  displayHeight = maxHeight;
                  displayWidth = maxHeight * aspectRatio;
                }

                // Create ultra-high resolution canvas (8x for PDF quality)
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                if (!ctx) {
                  // Fallback without canvas
                  doc.addImage(state.companyInfo.logo, 'PNG', 20, yPos, displayWidth, displayHeight);
                  resolve({ width: displayWidth, height: displayHeight });
                  return;
                }

                // Set canvas to very high resolution (minimum 300px for smaller logo, higher density)
                const highResScale = Math.max(10, 300 / Math.max(displayWidth, displayHeight));
                const canvasWidth = displayWidth * highResScale;
                const canvasHeight = displayHeight * highResScale;

                canvas.width = canvasWidth;
                canvas.height = canvasHeight;

                // Try different rendering approaches for best quality

                // Method 1: Crisp pixel-perfect rendering
                ctx.imageSmoothingEnabled = false;
                ctx.clearRect(0, 0, canvasWidth, canvasHeight);
                ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);
                let highQualityData = canvas.toDataURL('image/png', 1.0);

                // Method 2: If image is small, try high-quality smoothing instead
                if (img.width < 100 || img.height < 100) {
                  ctx.clearRect(0, 0, canvasWidth, canvasHeight);
                  ctx.imageSmoothingEnabled = true;
                  ctx.imageSmoothingQuality = 'high';
                  ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);
                  const smoothedData = canvas.toDataURL('image/png', 1.0);

                  // Use the larger data (usually higher quality)
                  if (smoothedData.length > highQualityData.length) {
                    highQualityData = smoothedData;
                  }
                }

                // Add to PDF with calculated display dimensions
                doc.addImage(highQualityData, 'PNG', 20, yPos, displayWidth, displayHeight);

                console.log('Logo processed (30px container for quality):', {
                  original: `${img.width}x${img.height}`,
                  aspectRatio: aspectRatio.toFixed(2),
                  display: `${displayWidth.toFixed(1)}x${displayHeight.toFixed(1)}`,
                  canvas: `${canvasWidth}x${canvasHeight}`,
                  scale: `${highResScale.toFixed(1)}x`,
                  dataSize: `${Math.round(highQualityData.length / 1024)}KB`,
                  container: '30x30px max'
                });

                resolve({ width: displayWidth, height: displayHeight });
              } catch (error) {
                console.warn('Logo processing error:', error);
                // Simple fallback with smaller size
                const fallbackSize = 30;
                doc.addImage(state.companyInfo.logo, 'PNG', 20, yPos, fallbackSize, fallbackSize);
                resolve({ width: fallbackSize, height: fallbackSize });
              }
            };

            img.onerror = () => {
              console.warn('Logo image failed to load');
              const fallbackSize = 30;
              try {
                doc.addImage(state.companyInfo.logo, 'PNG', 20, yPos, fallbackSize, fallbackSize);
              } catch (e) {
                console.error('Fallback logo add failed:', e);
              }
              resolve({ width: fallbackSize, height: fallbackSize });
            };

            img.src = state.companyInfo.logo;
          });

          // Update actual height for layout spacing
          actualLogoHeight = logoResult.height;

        } catch (error) {
          console.warn('Logo processing completely failed:', error);
          actualLogoHeight = 30; // Maintain layout spacing for smaller logo
        }
      }

      // Company info (positioned on the right side)
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      let companyInfoYPos = yPos;

      // Company name - only show if provided
      if (state.companyInfo?.name?.trim()) {
        doc.setFont('helvetica', 'bold');
        doc.text(state.companyInfo.name, companyInfoStartX, companyInfoYPos);
        companyInfoYPos += 8;
      } else {
        // Fallback to default if no company name provided
        doc.setFont('helvetica', 'bold');
        doc.text('PlomDesign Mobile', companyInfoStartX, companyInfoYPos);
        companyInfoYPos += 8;
      }

      // Company address
      doc.setFont('helvetica', 'normal');
      if (state.companyInfo?.address) {
        const address = state.companyInfo.address;
        if (address.street?.trim()) {
          doc.text(address.street, companyInfoStartX, companyInfoYPos);
          companyInfoYPos += 6;
        }
        const cityLine = [address.city, address.state, address.postalCode]
          .filter(field => field?.trim())
          .join(', ');
        if (cityLine) {
          doc.text(cityLine, companyInfoStartX, companyInfoYPos);
          companyInfoYPos += 6;
        }
        if (address.country?.trim()) {
          doc.text(address.country, companyInfoStartX, companyInfoYPos);
          companyInfoYPos += 6;
        }
      }

      // Contact information - only show fields that have values
      if (state.companyInfo?.phone?.trim()) {
        doc.text(`Phone: ${state.companyInfo.phone}`, companyInfoStartX, companyInfoYPos);
        companyInfoYPos += 6;
      }
      if (state.companyInfo?.email?.trim()) {
        doc.text(`Email: ${state.companyInfo.email}`, companyInfoStartX, companyInfoYPos);
        companyInfoYPos += 6;
      }
      if (state.companyInfo?.website?.trim()) {
        doc.text(`Website: ${state.companyInfo.website}`, companyInfoStartX, companyInfoYPos);
        companyInfoYPos += 6;
      }
      if (state.companyInfo?.taxId?.trim()) {
        doc.text(`Tax ID: ${state.companyInfo.taxId}`, companyInfoStartX, companyInfoYPos);
        companyInfoYPos += 6;
      }

      // Update yPos to be the maximum of logo bottom or company info bottom
      yPos = Math.max(yPos + actualLogoHeight, companyInfoYPos);

      // Add spacing before product table (removed invoice details for cleaner layout)
      yPos += 10;

      // Adjust line separator position
      const lineY = yPos;

      // Line separator
      doc.line(20, lineY, 190, lineY);

      // Table headers - New structure with consolidated Item Description and separate pricing columns
      let yPosition = lineY + 15;
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.text('Image', 20, yPosition);
      doc.text('Item Description', 55, yPosition);
      doc.text('Unit Price', 130, yPosition);
      doc.text('Quantity', 155, yPosition);
      doc.text('Total Price', 175, yPosition);

      // Header underline
      doc.line(20, yPosition + 2, 190, yPosition + 2);
      yPosition += 25;

      // Products
      doc.setFont('helvetica', 'normal');
      let totalAmount = 0;

      // Process products with images
      for (let i = 0; i < invoiceProducts.length; i++) {
        const product = invoiceProducts[i];

        // Check if we need a new page (account for larger images and multi-line descriptions)
        if (yPosition > 200) {
          doc.addPage();
          yPosition = 30;
        }

        // Add product image if available
        if (product.image) {
          try {
            // Load image and convert to base64
            const img = new Image();
            img.crossOrigin = 'anonymous';

            await new Promise((resolve, reject) => {
              img.onload = () => {
                try {
                  // Create canvas with much higher resolution for crisp images
                  const canvas = document.createElement('canvas');
                  const ctx = canvas.getContext('2d');

                  // Use much higher resolution - 200x200 pixels for very sharp quality
                  const canvasSize = 200;
                  canvas.width = canvasSize;
                  canvas.height = canvasSize;

                  if (ctx) {
                    // Set high-quality rendering settings
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    // Fill with white background first
                    ctx.fillStyle = 'white';
                    ctx.fillRect(0, 0, canvasSize, canvasSize);

                    // Calculate dimensions to maintain aspect ratio
                    const imgWidth = img.naturalWidth || img.width;
                    const imgHeight = img.naturalHeight || img.height;
                    const aspectRatio = imgWidth / imgHeight;

                    let drawWidth = canvasSize;
                    let drawHeight = canvasSize;
                    let offsetX = 0;
                    let offsetY = 0;

                    if (aspectRatio > 1) {
                      // Image is wider than tall
                      drawHeight = canvasSize / aspectRatio;
                      offsetY = (canvasSize - drawHeight) / 2;
                    } else {
                      // Image is taller than wide
                      drawWidth = canvasSize * aspectRatio;
                      offsetX = (canvasSize - drawWidth) / 2;
                    }

                    // Draw image with proper aspect ratio and centering
                    ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
                  }

                  // Get base64 data with maximum quality
                  const imgData = canvas.toDataURL('image/png', 1.0);

                  // Add image to PDF with larger size for better visibility
                  const pdfImageSize = 30; // Increased size in PDF
                  doc.addImage(imgData, 'PNG', 20, yPosition - 15, pdfImageSize, pdfImageSize);
                  resolve(true);
                } catch (error) {
                  console.warn('Failed to process image for product:', product.name, error);
                  resolve(false);
                }
              };

              img.onerror = () => {
                console.warn('Failed to load image for product:', product.name);
                resolve(false);
              };

              img.src = product.image;
            });
          } catch (error) {
            console.warn('Error processing image for product:', product.name, error);
          }
        } else {
          // Draw placeholder for no image with larger size
          const placeholderSize = 30;
          doc.setFillColor(240, 240, 240);
          doc.rect(20, yPosition - 15, placeholderSize, placeholderSize, 'F');
          doc.setFontSize(9);
          doc.setTextColor(150, 150, 150);
          doc.text('No', 30, yPosition - 5);
          doc.text('Image', 26, yPosition + 2);
          doc.setTextColor(0, 0, 0);
          doc.setFontSize(10);
        }

        // Consolidated Item Description column
        const productName = product.name.length > 20 ? product.name.substring(0, 17) + '...' : product.name;
        const category = product.category || 'N/A';
        const subcategory = product.subcategory || 'N/A';
        const material = product.material || 'N/A';

        // Display consolidated product information with line breaks
        doc.setFontSize(9);
        doc.text(productName, 55, yPosition - 8);
        doc.text(category, 55, yPosition - 2);
        doc.text(subcategory, 55, yPosition + 4);
        doc.text(material, 55, yPosition + 10);
        doc.setFontSize(10);

        // Calculate price for calculations
        const price = product.price && typeof product.price === 'number' ? product.price :
                     product.price && typeof product.price === 'string' && product.price !== '' ? parseFloat(product.price) : 0;

        // Unit Price column (no dollar symbol)
        const unitPriceText = price > 0 ? price.toFixed(2) : '-';
        doc.text(unitPriceText, 130, yPosition);

        // Quantity column
        const qtyText = product.totalQuantity.toString();
        doc.text(qtyText, 155, yPosition);

        // Total Price column (unit price × quantity, no dollar symbol)
        const totalPrice = price > 0 ? (price * product.totalQuantity) : 0;
        const totalPriceText = totalPrice > 0 ? totalPrice.toFixed(2) : '-';
        doc.text(totalPriceText, 175, yPosition);

        // Calculate total
        if (price > 0) {
          totalAmount += price * product.totalQuantity;
        }

        // Increase spacing to accommodate multi-line item descriptions
        yPosition += 45;
      }

      // Total section
      yPosition += 10;
      doc.line(20, yPosition, 190, yPosition);
      yPosition += 10;

      doc.setFont('helvetica', 'bold');
      doc.text('TOTAL:', 130, yPosition);
      doc.text(totalAmount.toFixed(2), 175, yPosition);

      // Footer
      yPosition += 20;
      doc.setFontSize(8);
      doc.setFont('helvetica', 'normal');
      doc.text('Generated by PlomDesign Mobile', 105, yPosition, { align: 'center' });
      doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, yPosition + 8, { align: 'center' });

      // Generate filename
      const timestamp = new Date().toISOString().split('T')[0];
      const canvasName = state.currentCanvas?.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'canvas';
      const filename = `PlomDesign_Invoice_${canvasName}_${timestamp}.pdf`;

      // Create blob URL for sharing
      const pdfBlob = doc.output('blob');
      const pdfUrl = URL.createObjectURL(pdfBlob);
      setLastGeneratedPdfUrl(pdfUrl);

      // Save the PDF (download)
      doc.save(filename);

      // Show success message with platform-specific guidance
      const capabilities = getSharingCapabilities();
      if (capabilities.nativeShareAvailable) {
        alert('PDF generated successfully! You can now use the Share button to share it through your device\'s native sharing options.');
      } else {
        alert('PDF generated and downloaded successfully! You can now use the Share button to share the invoice details.');
      }

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleShare = async () => {
    console.log('handleShare called');

    // Show the share modal instead of immediately trying to share
    setShowShareModal(true);
  };

  const handleShareOption = async (option: string) => {
    console.log('handleShareOption called with:', option);

    // For native sharing, we need to preserve the user gesture context
    // So we call navigator.share() IMMEDIATELY, before closing the modal
    if (option === 'native') {
      try {
        // Get share data using the existing function to avoid duplication
        const shareData = getShareData();

        console.log('Attempting native share immediately to preserve user gesture');

        // Call native share FIRST while user gesture is still valid
        const success = await shareViaMethod(option, shareData);

        // Only close modal AFTER the share attempt
        setShowShareModal(false);

        if (success) {
          console.log('Native share successful');
          // Show success feedback
          alert('Content shared successfully!');
        } else {
          console.log('Native share failed, showing fallback message');
          alert('Native sharing is not available on this device/browser. Please try another sharing option.');
        }

        return; // Exit early for native sharing
      } catch (error: any) {
        console.error('Error with native sharing:', error);
        setShowShareModal(false);
        alert(`Native sharing failed: ${error.message}. Please try another option.`);
        return;
      }
    }

    // For all other sharing methods, proceed as before
    setShowShareModal(false);
    setIsSharing(true);

    try {
      const timestamp = new Date().toISOString().split('T')[0];
      const canvasName = state.currentCanvas?.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'canvas';
      const filename = `PlomDesign_Invoice_${canvasName}_${timestamp}.pdf`;

      // Prepare share content
      const shareTitle = 'PlomDesign Invoice';
      let shareText = '';

      if (lastGeneratedPdfUrl) {
        shareText = `Invoice for ${state.currentCanvas?.name || 'Canvas'} - Generated by PlomDesign Mobile\n\nPDF file available for download.`;
      } else {
        // Create text summary of invoice
        const invoiceText = invoiceProducts.map(
          (product, index) => `${index + 1}. ${product.name} (Qty: ${product.quantity} - ${product.instances} instances)\nCategory: ${product.category} / ${product.subcategory}\nPrice: ${product.price}\nMaterial: ${product.material}`
        ).join('\n\n');

        const totalAmount = invoiceProducts.reduce((sum, product) => {
          return sum + (product.price * product.quantity);
        }, 0);

        shareText = `PlomDesign Invoice - ${state.currentCanvas?.name || 'Canvas'}\n\nProducts:\n${invoiceText}\n\nTotal: ${totalAmount.toFixed(2)}\n\nGenerated by PlomDesign Mobile`;
      }

      const shareData = {
        title: shareTitle,
        text: shareText,
        url: lastGeneratedPdfUrl || undefined,
        filename: lastGeneratedPdfUrl ? filename : undefined
      };

      const success = await shareViaMethod(option, shareData);

      if (success) {
        console.log(`Share via ${option} successful`);
        // Provide specific success feedback based on the sharing method
        const successMessages = {
          email: 'Email client opened successfully!',
          whatsapp: 'WhatsApp opened successfully!',
          telegram: 'Telegram opened successfully!',
          twitter: 'Twitter opened successfully!',
          facebook: 'Facebook opened successfully!',
          linkedin: 'LinkedIn opened successfully!',
          copy: 'Content copied to clipboard!',
          download: 'File downloaded successfully!'
        };
        const message = successMessages[option as keyof typeof successMessages] || 'Content shared successfully!';
        alert(message);
      } else {
        console.log(`Share via ${option} failed`);
        alert(`Failed to share via ${option}. Please try another option.`);
      }
    } catch (error: any) {
      console.error('Error sharing:', error);
      alert(`Error sharing invoice: ${error.message}. Please try again.`);
    } finally {
      setIsSharing(false);
    }
  };

  const getShareData = () => {
    const timestamp = new Date().toISOString().split('T')[0];
    const canvasName = state.currentCanvas?.name?.replace(/[^a-zA-Z0-9]/g, '_') || 'canvas';
    const filename = `PlomDesign_Invoice_${canvasName}_${timestamp}.pdf`;

    const shareTitle = 'PlomDesign Invoice';
    let shareText = '';

    if (lastGeneratedPdfUrl) {
      shareText = `Invoice for ${state.currentCanvas?.name || 'Canvas'} - Generated by PlomDesign Mobile\n\nPDF file available for download.`;
    } else {
      // Create text summary of invoice
      const invoiceText = invoiceProducts.map(
        (product, index) => `${index + 1}. ${product.name} (Qty: ${product.quantity} - ${product.instances} instances)\nCategory: ${product.category} / ${product.subcategory}\nPrice: ${product.price}\nMaterial: ${product.material}`
      ).join('\n\n');

      const totalAmount = invoiceProducts.reduce((sum, product) => {
        return sum + (product.price * product.quantity);
      }, 0);

      shareText = `PlomDesign Invoice - ${state.currentCanvas?.name || 'Canvas'}\n\nProducts:\n${invoiceText}\n\nTotal: ${totalAmount.toFixed(2)}\n\nGenerated by PlomDesign Mobile`;
    }

    return {
      title: shareTitle,
      text: shareText,
      url: lastGeneratedPdfUrl || undefined,
      filename: lastGeneratedPdfUrl ? filename : undefined
    };
  };





  return (
    <div style={{ padding: 0, width: '100%', height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <div style={{ background: '#fff', borderRadius: 16, padding: '24px 0px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)', flex: 1, margin: 0 }}>
        <h3 style={{ fontSize: 20, fontWeight: 600, color: '#2c3e50', marginTop: 0, marginBottom: 0 }}>Products</h3>
        {invoiceProducts.length === 0 ? (
          <p style={{ color: '#888', fontSize: 16, textAlign: 'center', margin: '24px 0' }}>No products on the canvas.</p>
        ) : (
          invoiceProducts.map((product) => (
            <div key={product.baseProductId} style={{ display: 'flex', alignItems: 'flex-start', marginBottom: 18, background: '#f6f8fa', borderRadius: 12, padding: 12 }}>
              {product.image ? (
                <img src={product.image} alt={product.name} style={{ width: 64, height: 64, borderRadius: 8, marginRight: 16, objectFit: 'cover', background: '#eee' }} />
              ) : (
                <div style={{ width: 64, height: 64, borderRadius: 8, background: '#eee', alignItems: 'center', justifyContent: 'center', display: 'flex', marginRight: 16 }}>
                  <span style={{ color: '#aaa' }}>No Image</span>
                </div>
              )}
              <div style={{ flex: 1 }}>
                <div style={{ fontSize: 18, fontWeight: 600, color: '#2c3e50', marginBottom: 4 }}>{product.name}</div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>Category: {product.category} / {product.subcategory}</div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>
                  Quantity: {product.totalQuantity}
                  {product.instances > 1 && (
                    <span style={{ color: '#6b7280', fontSize: 13, marginLeft: 8 }}>
                      ({product.instances} instances on canvas)
                    </span>
                  )}
                </div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>Price: {product.price !== undefined && product.price !== null && (typeof product.price === 'number' || (typeof product.price === 'string' && product.price !== '')) ? product.price : '-'}</div>
                <div style={{ fontSize: 15, color: '#555', marginBottom: 2 }}>Material: {product.material ? product.material : 'N/A'}</div>
              </div>
            </div>
          ))
        )}
        <div style={{ height: 16 }} />

        {/* Sharing capabilities info */}
        <div style={{ padding: '0 12px', marginBottom: 16 }}>
          <p style={{
            fontSize: 12,
            color: '#6b7280',
            margin: 0,
            textAlign: 'center',
            fontStyle: 'italic'
          }}>
            {(() => {
              const capabilities = getSharingCapabilities();
              if (capabilities.nativeShareAvailable) {
                return `✨ Native ${capabilities.platform} sharing available`;
              } else {
                return '📋 Text sharing and file download available';
              }
            })()}
          </p>
        </div>

        <div style={{ display: 'flex', gap: 12, justifyContent: 'space-between', padding: '0 12px' }}>
          <button
            style={{
              flex: 1,
              height: 40,
              background: isGenerating ? '#9ca3af' : '#667eea',
              borderRadius: 8,
              color: '#fff',
              fontSize: 14,
              fontWeight: 600,
              border: 'none',
              cursor: isGenerating ? 'not-allowed' : 'pointer',
              opacity: isGenerating ? 0.7 : 1
            }}
            onClick={handleGeneratePdf}
            disabled={isGenerating}
          >
            {isGenerating ? 'Generating...' : 'Generate PDF'}
          </button>
          <button
            style={{
              flex: 1,
              height: 40,
              border: `2px solid ${isSharing ? '#9ca3af' : '#667eea'}`,
              borderRadius: 8,
              color: isSharing ? '#9ca3af' : '#667eea',
              fontSize: 14,
              fontWeight: 600,
              background: 'none',
              cursor: isSharing ? 'not-allowed' : 'pointer',
              opacity: isSharing ? 0.7 : 1
            }}
            onClick={handleShare}
            disabled={isSharing}
          >
            {isSharing ? 'Sharing...' : lastGeneratedPdfUrl ? 'Share PDF' : 'Share Invoice'}
          </button>
        </div>
      </div>

      {/* Share Modal */}
      <ShareModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        shareData={getShareData()}
        onShareOption={handleShareOption}
      />
    </div>
  );
};

export default InvoicePdfScreen;
