// Simple HTTPS server using http-server package
// Run: npm install -g http-server
// Then: node serve-https-simple.js

const { spawn } = require('child_process');
const path = require('path');

console.log(`
🚀 Starting HTTPS server for Web Share API testing...

📋 Prerequisites:
1. Install http-server globally: npm install -g http-server
2. Build the project: npm run build

🔒 Starting HTTPS server...
`);

// Start http-server with SSL
const server = spawn('http-server', [
  path.join(__dirname, 'dist'),
  '-p', '3000',
  '-S',  // Enable HTTPS
  '-C', 'cert.pem',  // Certificate file (will be created if not exists)
  '-K', 'key.pem',   // Key file (will be created if not exists)
  '-o'   // Open browser automatically
], { stdio: 'inherit' });

server.on('error', (err) => {
  if (err.code === 'ENOENT') {
    console.error(`
❌ http-server not found!

Please install it globally:
npm install -g http-server

Or use the alternative method:
npx http-server dist -p 3000 -S -o

Or use the built-in serve-https.js:
node serve-https.js
    `);
  } else {
    console.error('Server error:', err);
  }
});

server.on('close', (code) => {
  console.log(`Server stopped with code ${code}`);
});

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping server...');
  server.kill();
  process.exit(0);
});
