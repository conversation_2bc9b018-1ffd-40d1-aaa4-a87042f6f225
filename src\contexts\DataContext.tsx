import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Product, Canvas, Settings, User, LicenseInfo, CompanyInfo } from '../types';
import { databaseService } from '../database/DatabaseService';

interface DataState {
  products: Product[];
  canvases: Canvas[];
  currentCanvas: Canvas | null;
  settings: Settings | null;
  user: User | null;
  licenseInfo: LicenseInfo | null;
  companyInfo: CompanyInfo | null;
  isLoading: boolean;
  error: string | null;
}

type DataAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_PRODUCTS'; payload: Product[] }
  | { type: 'ADD_PRODUCT'; payload: Product }
  | { type: 'UPDATE_PRODUCT'; payload: Product }
  | { type: 'DELETE_PRODUCT'; payload: string }
  | { type: 'SET_CANVASES'; payload: Canvas[] }
  | { type: 'SET_CURRENT_CANVAS'; payload: Canvas | null }
  | { type: 'UPDATE_CANVAS'; payload: Canvas }
  | { type: 'SET_SETTINGS'; payload: Settings }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_LICENSE_INFO'; payload: LicenseInfo | null }
  | { type: 'SET_COMPANY_INFO'; payload: CompanyInfo | null };

const initialState: DataState = {
  products: [],
  canvases: [],
  currentCanvas: null,
  settings: null,
  user: null,
  licenseInfo: null,
  companyInfo: null,
  isLoading: false,
  error: null,
};

function dataReducer(state: DataState, action: DataAction): DataState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    case 'SET_PRODUCTS':
      return { ...state, products: action.payload };
    case 'ADD_PRODUCT':
      return { ...state, products: [...state.products, action.payload] };
    case 'UPDATE_PRODUCT':
      return {
        ...state,
        products: state.products.map(p =>
          p.id === action.payload.id ? action.payload : p
        ),
      };
    case 'DELETE_PRODUCT':
      return {
        ...state,
        products: state.products.filter(p => p.id !== action.payload),
      };
    case 'SET_CANVASES':
      return { ...state, canvases: action.payload };
    case 'SET_CURRENT_CANVAS':
      return { ...state, currentCanvas: action.payload };
    case 'UPDATE_CANVAS':
      return {
        ...state,
        canvases: state.canvases.map(c =>
          c.id === action.payload.id ? action.payload : c
        ),
        currentCanvas: state.currentCanvas?.id === action.payload.id ? action.payload : state.currentCanvas,
      };
    case 'SET_SETTINGS':
      return { ...state, settings: action.payload };
    case 'SET_USER':
      return { ...state, user: action.payload };
    case 'SET_LICENSE_INFO':
      return { ...state, licenseInfo: action.payload };
    case 'SET_COMPANY_INFO':
      return { ...state, companyInfo: action.payload };
    default:
      return state;
  }
}

interface DataContextType {
  state: DataState;
  // Product operations
  loadProducts: () => Promise<void>;
  saveProduct: (product: Omit<Product, 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateProduct: (product: Product) => Promise<void>;
  deleteProduct: (productId: string) => Promise<void>;

  // Canvas operations
  loadCanvases: () => Promise<void>;
  saveCanvas: (canvas: Omit<Canvas, 'createdAt' | 'updatedAt'>) => Promise<void>;
  setCurrentCanvas: (canvas: Canvas | null) => void;
  updateCurrentCanvas: (updates: Partial<Canvas>) => void;

  // Settings operations
  loadSettings: () => Promise<void>;
  saveSettings: (settings: Omit<Settings, 'createdAt' | 'updatedAt'>) => Promise<void>;

  // User operations
  loadUser: () => Promise<void>;
  saveUser: (user: Omit<User, 'createdAt' | 'updatedAt'>) => Promise<void>;
  logout: () => Promise<void>;

  // License operations
  loadLicenseInfo: (userId: string) => Promise<void>;
  saveLicenseInfo: (licenseInfo: Omit<LicenseInfo, 'createdAt' | 'updatedAt'>) => Promise<void>;

  // Company operations
  loadCompanyInfo: () => Promise<void>;
  saveCompanyInfo: (companyInfo: Omit<CompanyInfo, 'createdAt' | 'updatedAt'>) => Promise<void>;

  // Utility operations
  initializeApp: () => Promise<void>;
  clearAllData: () => Promise<void>;
  importDatabase: (data: Uint8Array) => Promise<void>;
  clearError: () => void;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

interface DataProviderProps {
  children: ReactNode;
}

export const DataProvider: React.FC<DataProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Product operations
  const loadProducts = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const products = await databaseService.getAllProducts();
      dispatch({ type: 'SET_PRODUCTS', payload: products });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load products' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const saveProduct = async (product: Omit<Product, 'createdAt' | 'updatedAt'>) => {
    console.log('DataContext.saveProduct called with:', product);
    try {
      console.log('Calling databaseService.saveProduct...');
      await databaseService.saveProduct(product);
      console.log('databaseService.saveProduct completed successfully');

      const updatedProduct: Product = {
        ...product,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      console.log('Dispatching ADD_PRODUCT action with:', updatedProduct);
      dispatch({ type: 'ADD_PRODUCT', payload: updatedProduct });
      console.log('Product save operation completed successfully');
    } catch (error) {
      console.error('Error in DataContext.saveProduct:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save product';
      console.error('Setting error state:', errorMessage);
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    }
  };

  const updateProduct = async (product: Product) => {
    try {
      const updatedProduct = { ...product, updatedAt: new Date().toISOString() };
      await databaseService.saveProduct(updatedProduct);
      dispatch({ type: 'UPDATE_PRODUCT', payload: updatedProduct });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to update product' });
      throw error;
    }
  };

  const deleteProduct = async (productId: string) => {
    try {
      await databaseService.deleteProduct(productId);
      dispatch({ type: 'DELETE_PRODUCT', payload: productId });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to delete product' });
      throw error;
    }
  };

  // Canvas operations
  const loadCanvases = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const canvases = await databaseService.getAllCanvases();
      dispatch({ type: 'SET_CANVASES', payload: canvases });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load canvases' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const saveCanvas = async (canvas: Omit<Canvas, 'createdAt' | 'updatedAt'>) => {
    try {
      await databaseService.saveCanvas(canvas);
      const updatedCanvas: Canvas = {
        ...canvas,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      dispatch({ type: 'UPDATE_CANVAS', payload: updatedCanvas });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to save canvas' });
      throw error;
    }
  };

  const setCurrentCanvas = (canvas: Canvas | null) => {
    dispatch({ type: 'SET_CURRENT_CANVAS', payload: canvas });
  };

  const updateCurrentCanvas = (updates: Partial<Canvas>) => {
    if (state.currentCanvas) {
      const updatedCanvas = { ...state.currentCanvas, ...updates, updatedAt: new Date().toISOString() };
      dispatch({ type: 'UPDATE_CANVAS', payload: updatedCanvas });
    }
  };

  // Settings operations
  const loadSettings = async () => {
    try {
      const settings = await databaseService.getSettings();
      if (settings) {
        dispatch({ type: 'SET_SETTINGS', payload: settings });
      } else {
        // Create default settings
        const defaultSettings: Settings = {
          id: 'default',
          theme: 'light',
          autoSave: true,
          autoSaveInterval: 30000,
          visibleCategories: {},
          removedCategories: [],
          deletedCategories: [],
          removedSubcategories: [],
          deletedSubcategories: [],
          removedProducts: [],
          deletedProducts: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        await saveSettings(defaultSettings);
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load settings' });
    }
  };

  const saveSettings = async (settings: Omit<Settings, 'createdAt' | 'updatedAt'>) => {
    try {
      await databaseService.saveSettings(settings);
      const updatedSettings: Settings = {
        ...settings,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      dispatch({ type: 'SET_SETTINGS', payload: updatedSettings });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to save settings' });
      throw error;
    }
  };

  // User operations
  const loadUser = async () => {
    try {
      const user = await databaseService.getCurrentUser();
      dispatch({ type: 'SET_USER', payload: user });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load user' });
    }
  };

  const saveUser = async (user: Omit<User, 'createdAt' | 'updatedAt'>) => {
    try {
      await databaseService.saveUser(user);
      const updatedUser: User = {
        ...user,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      dispatch({ type: 'SET_USER', payload: updatedUser });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to save user' });
      throw error;
    }
  };

  const logout = async () => {
    try {
      if (state.user) {
        const loggedOutUser = { ...state.user, isLoggedIn: false };
        await databaseService.saveUser(loggedOutUser);
        dispatch({ type: 'SET_USER', payload: null });
        dispatch({ type: 'SET_LICENSE_INFO', payload: null });
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to logout' });
      throw error;
    }
  };

  // License operations
  const loadLicenseInfo = async (userId: string) => {
    try {
      const licenseInfo = await databaseService.getLicenseInfo(userId);
      dispatch({ type: 'SET_LICENSE_INFO', payload: licenseInfo });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load license info' });
    }
  };

  const saveLicenseInfo = async (licenseInfo: Omit<LicenseInfo, 'createdAt' | 'updatedAt'>) => {
    try {
      await databaseService.saveLicenseInfo(licenseInfo);
      const updatedLicenseInfo: LicenseInfo = {
        ...licenseInfo,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      dispatch({ type: 'SET_LICENSE_INFO', payload: updatedLicenseInfo });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to save license info' });
      throw error;
    }
  };

  // Company operations
  const loadCompanyInfo = async () => {
    try {
      const companyInfo = await databaseService.getCompanyInfo();
      dispatch({ type: 'SET_COMPANY_INFO', payload: companyInfo });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to load company info' });
    }
  };

  const saveCompanyInfo = async (companyInfo: Omit<CompanyInfo, 'createdAt' | 'updatedAt'>) => {
    try {
      // Ensure we have a valid company info object
      const validCompanyInfo = {
        id: companyInfo.id || 'default',
        name: companyInfo.name || '',
        address: companyInfo.address || {
          street: '',
          city: '',
          state: '',
          postalCode: '',
          country: ''
        },
        phone: companyInfo.phone || '',
        email: companyInfo.email || '',
        taxId: companyInfo.taxId || '',
        website: companyInfo.website || '',
        logo: companyInfo.logo || ''
      };

      await databaseService.saveCompanyInfo(validCompanyInfo);

      const updatedCompanyInfo: CompanyInfo = {
        ...validCompanyInfo,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      dispatch({ type: 'SET_COMPANY_INFO', payload: updatedCompanyInfo });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save company info';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw new Error(errorMessage);
    }
  };

  // Utility operations
  const initializeApp = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      await databaseService.init();

      // Load all data in parallel
      await Promise.all([
        loadProducts(),
        loadCanvases(),
        loadSettings(),
        loadUser(),
        loadCompanyInfo(),
      ]);

      // Load license info if user exists
      if (state.user) {
        await loadLicenseInfo(state.user.id);
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to initialize app' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const clearAllData = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      await databaseService.clearAllData();

      // Reset state
      dispatch({ type: 'SET_PRODUCTS', payload: [] });
      dispatch({ type: 'SET_CANVASES', payload: [] });
      dispatch({ type: 'SET_CURRENT_CANVAS', payload: null });
      dispatch({ type: 'SET_USER', payload: null });
      dispatch({ type: 'SET_LICENSE_INFO', payload: null });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to clear data' });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const importDatabase = async (data: Uint8Array) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      await databaseService.importDatabase(data);

      // Reload all data after import
      await Promise.all([
        loadProducts(),
        loadCanvases(),
        loadSettings(),
        loadUser(),
      ]);

      // Load license info if user exists
      if (state.user) {
        await loadLicenseInfo(state.user.id);
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Failed to import database' });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const clearError = () => {
    dispatch({ type: 'SET_ERROR', payload: null });
  };

  // Initialize app on mount
  useEffect(() => {
    initializeApp();
  }, []);

  const value: DataContextType = {
    state,
    loadProducts,
    saveProduct,
    updateProduct,
    deleteProduct,
    loadCanvases,
    saveCanvas,
    setCurrentCanvas,
    updateCurrentCanvas,
    loadSettings,
    saveSettings,
    loadUser,
    saveUser,
    logout,
    loadLicenseInfo,
    saveLicenseInfo,
    loadCompanyInfo,
    saveCompanyInfo,
    initializeApp,
    clearAllData,
    importDatabase,
    clearError,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};