{"name": "PlomDesignMobile", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "web": "react-native start --reset-cache", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@expo/metro-runtime": "^5.0.4", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native/new-app-screen": "0.81.1", "@react-navigation/drawer": "^7.5.7", "@react-navigation/native": "^7.1.17", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "react": "19.1.0", "react-dom": "^19.1.1", "react-native": "0.81.1", "react-native-gesture-handler": "^2.28.0", "react-native-reanimated": "^4.0.3", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.15.4", "react-native-sqlite-storage": "^6.0.1", "react-native-vector-icons": "^10.3.0", "react-native-web": "^0.21.1", "react-native-worklets": "^0.4.2", "react-native-worklets-core": "^1.6.2", "react-native-share": "^12.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "20.0.0", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "20.0.0", "@react-native/babel-preset": "0.81.1", "@react-native/eslint-config": "0.81.1", "@react-native/metro-config": "0.81.1", "@react-native/typescript-config": "0.81.1", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-native-sqlite-storage": "^6.0.5", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "^5.8.3"}, "engines": {"node": ">=20"}}